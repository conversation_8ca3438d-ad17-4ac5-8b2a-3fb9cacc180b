eventLst:
  - name: "个人贷款申请"
    description: "个人贷款申请"
    key: "Start"
    type: "Start"
    input:
      - userID: "用户ID"
        amount: "贷款金额"

  - name: "参数清洗"
    description: "参数清洗"
    key: "dataFilter"
    type: "Filter"
    input:
      - amount: "贷款金额"
  
  - name: "基础校验"
    description: "基础校验"
    key: "dataValidator"
    type: "Validator"
  
  - name: "征信查询"
    description: "征信查询"
    key: "dataConnector"
    type: "Connector"

  - name: "额度计算"
    description: "额度计算"
    key: "dataCalculator"
    type: "Calculator"

  - name: "自动审核"
    description: "自动审核"
    key: "dataDecisionMaker"
    type: "DecisionMaker"

  - name: "发送审批通过"
    description: "发送审批通过"
    key: "sucessNotifier"
    type: "Notifier"

  - name: "发送拒绝通知"
    description: "发送拒绝通知"
    key: "failNotifier"
    type: "Notifier"

  - name: "创建人工审核任务"
    description: "创建人工审核任务"
    key: "dataStateMachine"
    type: "StateMachine"
  
  - name: "发送代办提醒"
    description: "发送代办提醒"
    key: "personNotifier"
    type: "Notifier"
  








    


  








