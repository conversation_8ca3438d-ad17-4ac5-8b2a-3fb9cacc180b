componentStyles:
  Start:
    icon: "play_circle_filled"   # 替换为 play_circle_filled
    color: "#4CAF50"
    backgroundColor: "#E8F5E9"
    borderColor: "#81C784"

  End:
    icon: "stop"
    color: "#F44336"
    backgroundColor: "#FFEBEE"
    borderColor: "#E57373"

  Validator:
    icon: "check_circle"
    color: "#2196F3"
    backgroundColor: "#E3F2FD"
    borderColor: "#64B5F6"

  DecisionMaker:
    icon: "help_outline"
    color: "#FF9800"
    backgroundColor: "#FFF3E0"
    borderColor: "#FFB74D"

  Calculator:
    icon: "functions"            # 替换为 functions（∑符号，兼容性最好）
    color: "#9C27B0"
    backgroundColor: "#F3E5F5"
    borderColor: "#BA68C8"

  Connector:
    icon: "link"
    color: "#00BCD4"
    backgroundColor: "#E0F7FA"
    borderColor: "#4DD0E1"

  Notifier:
    icon: "notifications"
    color: "#FFC107"
    backgroundColor: "#FFF8E1"
    borderColor: "#FFD54F"

  StateMachine:
    icon: "settings"
    color: "#607D8B"
    backgroundColor: "#ECEFF1"
    borderColor: "#90A4AE"

  Filter:
    icon: "filter_list"
    color: "#795548"
    backgroundColor: "#EFEBE9"
    borderColor: "#A1887F"