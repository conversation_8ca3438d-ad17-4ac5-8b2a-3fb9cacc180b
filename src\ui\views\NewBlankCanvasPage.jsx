import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import ReactFlow, {
  Background,
  Controls,
  MiniMap,
  addEdge,
  ReactFlowProvider,
  useReactFlow,
  Handle,
  Position,
  useNodesState,
  useEdgesState,
  StraightEdge,
  StepEdge,
} from 'reactflow';
import 'reactflow/dist/base.css';
import yaml from 'js-yaml';
import { fetchText, getEventsByType } from '../../utils/core-utils/fetchUtil';
import BankingDecisionNodeHelper, { ChildNodeSettingModal } from './banking/BankingDecisionNodeHelper';
import * as BankingService from '../../services/domain-services/BankingService';
import * as EducationService from '../../services/domain-services/EducationService';
import { useLocation } from 'react-router-dom';
import CustomNode from '../components/common/CustomNode';
import { nodeTypes as baseNodeTypes, edgeTypes } from '../components/common/reactflowTypes';
import { sleep, patchNodesWithHandlers } from '../../utils/core-utils/fetchUtil';
import { uploadTemplate } from '../../services/api';
import { getNodeEventInfo } from '../components/common/nodeEventHelper';

function getEnv(key) {
  return import.meta.env[key];
}

function getExecuteNodeFunction(domainName) {
  console.log('domainName ：', domainName);
  if (domainName === '银行领域') return BankingService.executeNodeFunction;
  if (domainName === '教育领域') return EducationService.executeNodeFunction;
  return async () => 'stop';
}

function getNodeTypes(handlers) {
  return {
    custom: (props) => <CustomNode {...props} {...handlers} />
  };
}

export const nodeTypes = {
  custom: CustomNode
};

function CanvasContent({ functionList, stylesMap, eventFileName, domainName }) {
  const location = useLocation();
  const [nodes, setNodesRaw, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const reactFlowWrapper = useRef(null);
  const { screenToFlowPosition } = useReactFlow();

  // 新增：播放动画高亮状态
  const [playingNodeId, setPlayingNodeId] = useState(null);

  // 新增：用ref保存最新nodes
  const nodesRef = useRef(nodes);
  useEffect(() => {
    nodesRef.current = nodes;
  }, [nodes]);

  // 新增：用ref保存最新edges
  const edgesRef = useRef(edges);
  useEffect(() => {
    edgesRef.current = edges;
  }, [edges]);

  // patch nodes，给每个node的data加上事件和playingNodeId
  const patchNodes = useCallback((nds, playingId = playingNodeId) => {
    if (!Array.isArray(nds)) return [];
    return nds.map(n => ({
      ...n,
      data: {
        type: n.data.type, // 保证type字段一定存在
        ...n.data,
        onDelete: handleDeleteNode,
        onSetting: handleSettingClick,
        onPlay: handlePlayFromNode,
        onNodeContextMenu: handleNodeContextMenu,
        playingNodeId: playingId,
      }
    }));
  }, [playingNodeId]);

  // 用于setNodes，自动patch，支持回调和直接赋值
  const setNodes = useCallback((nds) => {
    if (typeof nds === 'function') {
      setNodesRaw(prev => patchNodes(nds(Array.isArray(prev) ? prev : [])));
    } else {
      setNodesRaw(patchNodes(Array.isArray(nds) ? nds : []));
    }
  }, [setNodesRaw, patchNodes]);

  // 弹窗相关状态
  const [modalOpen, setModalOpen] = useState(false);
  const [pendingNode, setPendingNode] = useState(null); // {item, position}
  const [eventOptions, setEventOptions] = useState([]);
  const [selectedEventKey, setSelectedEventKey] = useState('');
  // 银行业务专用决策器弹窗
  const [decisionHelperOpen, setDecisionHelperOpen] = useState(false);
  const [decisionHelperInfo, setDecisionHelperInfo] = useState(null); // {item, position}
  const [decisionEventOptions, setDecisionEventOptions] = useState([]);
  const [decisionSelectedEventKey, setDecisionSelectedEventKey] = useState('');

  // 新增设置弹窗相关状态
  const [settingModalOpen, setSettingModalOpen] = useState(false);
  const [settingNodeId, setSettingNodeId] = useState(null);
  const [settingType, setSettingType] = useState('');
  const [settingEventOptions, setSettingEventOptions] = useState([]);
  const [settingEventKey, setSettingEventKey] = useState('');
  const [settingTypeOptions, setSettingTypeOptions] = useState([]);

  // 分享弹窗相关状态
  const [showShareModal, setShowShareModal] = useState(false);
  const [shareFileName, setShareFileName] = useState('');
  const [shareError, setShareError] = useState('');

  // 右键说明面板相关状态
  const [infoPanelOpen, setInfoPanelOpen] = useState(false);
  const [infoPanelData, setInfoPanelData] = useState({ description: '', input: null, nodeName: '', nodeId: '' });

  // 新增inputParams和setInputParams
  const [inputParams, setInputParams] = useState({});

  // 2. 在infoPanelData.input变化时初始化inputParams
  useEffect(() => {
    if (infoPanelOpen && infoPanelData.input) {
      const defaults = {};
      if (Array.isArray(infoPanelData.input)) {
        Object.entries(infoPanelData.input[0] || {}).forEach(([k, v]) => { defaults[k] = v; });
      } else {
        Object.entries(infoPanelData.input).forEach(([k, v]) => { defaults[k] = v; });
      }
      setInputParams(defaults);
    }
  }, [infoPanelOpen, infoPanelData.input]);

  // 处理设置按钮点击（必须放在patchNodes后）
  const handleSettingClick = useCallback(
    async (nodeId) => {
      const currentNodes = nodesRef.current;
      const node = currentNodes.find(n => n.id === nodeId);
      if (!node) return;
      // 组装 typeOptions 为 [{label, value}]
      const typeOptions = functionList.map(f => ({
        label: f.name,
        value: f.type
      }));
      let type = node.data.type;
      if (!typeOptions.some(opt => opt.value === type)) {
        type = typeOptions[0]?.value || '';
      }
      // 获取事件
      let events = [];
      if (eventFileName && type) {
        events = await getEventsByType(eventFileName, type);
      }
      setSettingTypeOptions(typeOptions);
      setSettingType(type);
      setSettingEventOptions(events);
      setSettingEventKey(events[0]?.key || '');
      setSettingNodeId(nodeId);
      setSettingModalOpen(true);
    },
    [functionList, eventFileName]
  );

  // 删除节点
  const handleDeleteNode = useCallback((id) => {
    setNodes((nds) => nds.filter((n) => n.id !== id));
    setEdges((eds) => eds.filter((e) => e.source !== id && e.target !== id));
  }, [setNodes, setEdges]);

  // 节点播放动画和执行逻辑（参考AIGeneratePage.jsx，循环中每次都用最新edgesRef.current）
  const handlePlayFromNode = React.useCallback(async (startId) => {
    function sleep(ms) { return new Promise(resolve => setTimeout(resolve, ms)); }
    let currentId = startId;
    let visited = new Set();
    while (currentId && !visited.has(currentId)) {
      visited.add(currentId);
      setPlayingNodeId(currentId);
      console.log('执行到节点:', currentId);
      await sleep(1000);
      const currentNode = nodesRef.current.find(n => n.id === currentId);
      console.log('当前节点对象:', currentNode);
      console.log('当前节点 inputParams:', currentNode?.data?.inputParams);
      // 强制合并inputParams（如state中有inputParams也合并）
      const latestInputParams = currentNode?.data?.inputParams && Object.keys(currentNode.data.inputParams).length > 0
        ? currentNode.data.inputParams
        : (inputParams || {});
      const nodeWithParams = {
        ...currentNode,
        data: {
          ...currentNode.data,
          inputParams: latestInputParams
        }
      };
      console.log('executeNodeFunction 传递的 node:', nodeWithParams);
      const result = await getExecuteNodeFunction(domainName)(nodeWithParams);
      console.log('节点执行结果:', result);
      if (result === 'stop') break;
      if (result === 'branch-true' || result === 'branch-false') {
        const branch = result === 'branch-true' ? 'true' : 'false';
        console.log('edgesRef.current:', edgesRef.current);
        const nextEdge = edgesRef.current.find(e => e.source === currentId && e.data && e.data.branch === branch);
        console.log('分支查找:', branch, nextEdge);
        currentId = nextEdge ? nextEdge.target : null;
      } else {
        console.log('edgesRef.current:', edgesRef.current);
        const nextEdge = edgesRef.current.find(e => e.source === currentId);
        console.log('顺序查找:', nextEdge);
        currentId = nextEdge ? nextEdge.target : null;
      }
    }
    setPlayingNodeId(null);
  }, [domainName]);

  // 右键节点事件处理
  const handleNodeContextMenu = async (id, nodeName) => {
    const info = await getNodeEventInfo(domainName, nodeName);
    setInfoPanelData({
      description: info?.description || '无描述',
      input: info?.input || null,
      nodeName,
      nodeId: id
    });
    setInfoPanelOpen(true);
  };

  // playingNodeId变化时，patch一遍nodes
  useEffect(() => {
    setNodesRaw(nds => patchNodes(nds, playingNodeId));
  }, [playingNodeId, patchNodes]);

  // 如果有模板内容，初始化画布
  useEffect(() => {
    if (location.state && location.state.template) {
      setNodes(location.state.template.nodes || []);
      setEdges(location.state.template.edges || []);
      // 如果模板包含inputParams，也一并加载
      if (location.state.template.inputParams) {
        setInputParams(location.state.template.inputParams);
      }
    }
  }, [location.state, setNodes, setEdges]);

  // 拖拽开始
  const onDragStart = (event, item) => {
    event.dataTransfer.setData('application/reactflow', JSON.stringify(item));
    event.dataTransfer.effectAllowed = 'move';
  };

  // 允许拖拽经过
  const onDragOver = (event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  };

  // 放下时弹出模态框（异步获取事件）
  const onDrop = useCallback(
    async (event) => {
      event.preventDefault();
      const data = event.dataTransfer.getData('application/reactflow');
      if (!data) return;
      const item = JSON.parse(data);
      let position = { x: 0, y: 0 };
      if (typeof screenToFlowPosition === 'function') {
        position = screenToFlowPosition({ x: event.clientX, y: event.clientY });
      } else {
        position = { x: event.clientX, y: event.clientY };
      }
      // 银行业务专用决策器弹窗
      if (domainName === '银行领域' && item.type === 'DecisionMaker') {
        let events = [];
        if (eventFileName) {
          events = await getEventsByType(eventFileName, item.type);
        }
        setDecisionEventOptions(events);
        setDecisionSelectedEventKey(events[0]?.key || '');
        setDecisionHelperInfo({ item, position });
        setDecisionHelperOpen(true);
        return;
      }
      // 动态获取事件
      let events = [];
      if (eventFileName) {
        events = await getEventsByType(eventFileName, item.type);
      }
      setEventOptions(events);
      setSelectedEventKey(events[0]?.key || '');
      setPendingNode({ item, position });
      setModalOpen(true);
    },
    [screenToFlowPosition, eventFileName, domainName]
  );

  // 银行业务决策器弹窗确认
  const handleDecisionHelperOk = (count, eventKey) => {
    if (!decisionHelperInfo) return;
    const { item, position } = decisionHelperInfo;
    const style = stylesMap[item.type] || {};
    const selectedEvent = decisionEventOptions.find(e => e.key === eventKey);
    // 主节点
    const mainNodeId = `${item.key}_${+new Date()}`;
    const mainNode = {
      id: mainNodeId,
      type: 'custom',
      position,
      data: {
        label: item.name + (selectedEvent ? `（${selectedEvent.name}）` : ''),
        name: item.name,
        icon: style.icon,
        color: style.color,
        background: style.backgroundColor,
        border: style.borderColor ? `1.5px solid ${style.borderColor}` : undefined,
        type: item.type,
      },
    };
    // 子节点自动排列在主节点下方，data.type赋值为functionList第一个type
    const defaultType = functionList.find(f => f.type !== 'Start')?.type || functionList[0]?.type || 'Validator';
    const childNodes = Array.from({ length: count }).map((_, i) => {
      return {
        id: `${mainNodeId}_child_${i}`,
        type: 'custom',
        position: { x: position.x + i * 120 - (count-1)*60, y: position.y + 120 },
        data: {
          label: `子节点${i+1}` + (selectedEvent ? `（${selectedEvent.name}）` : ''),
          name: `子节点${i+1}`,
          icon: 'subdirectory_arrow_right',
          color: '#607D8B',
          background: '#ECEFF1',
          border: '1.5px solid #90A4AE',
          type: defaultType, // 关键：赋值为真实业务类型
        },
      };
    });
    // 连线
    const newEdges = childNodes.map(child => ({
      id: `${mainNodeId}_to_${child.id}`,
      source: mainNodeId,
      target: child.id,
      type: 'dashed',
    }));
    setNodes(nds => nds.concat(mainNode, ...childNodes));
    setEdges(eds => eds.concat(...newEdges));
    setDecisionHelperOpen(false);
    setDecisionHelperInfo(null);
    setDecisionEventOptions([]);
    setDecisionSelectedEventKey('');
  };
  const handleDecisionHelperCancel = () => {
    setDecisionHelperOpen(false);
    setDecisionHelperInfo(null);
    setDecisionEventOptions([]);
    setDecisionSelectedEventKey('');
  };

  // 确认添加节点
  const handleModalOk = () => {
    if (!pendingNode) return;
    const { item, position } = pendingNode;
    const style = stylesMap[item.type] || {};
    const selectedEvent = eventOptions.find(e => e.key === selectedEventKey);
    const eventName = selectedEvent ? selectedEvent.name : item.name;
    const newNode = {
      id: `${item.key}_${+new Date()}`,
      type: 'custom',
      position,
      data: {
        label: (item.name ? item.name + '：' : '') + eventName,
        name: eventName,
        icon: style.icon,
        color: style.color,
        background: style.backgroundColor,
        border: style.borderColor ? `1.5px solid ${style.borderColor}` : undefined,
        type: item.type,
      },
    };
    setNodes((nds) => nds.concat(newNode));
    setModalOpen(false);
    setPendingNode(null);
    setEventOptions([]);
    setSelectedEventKey('');
  };

  // 取消弹窗
  const handleModalCancel = () => {
    setModalOpen(false);
    setPendingNode(null);
    setEventOptions([]);
    setSelectedEventKey('');
  };

  // 支持节点连线
  const onConnect = useCallback((params) => setEdges((eds) => addEdge({ ...params, type: 'dashed' }, eds)), [setEdges]);

  // type切换时联动事件
  const handleSettingTypeChange = async (type) => {
    setSettingType(type);
    let events = [];
    if (eventFileName && type) {
      events = await getEventsByType(eventFileName, type);
    }
    setSettingEventOptions(events);
    setSettingEventKey(events[0]?.key || '');
  };

  // 设置弹窗确认
  const handleSettingModalOk = () => {
    setNodes(nds => nds.map(n => {
      if (n.id === settingNodeId) {
        const selectedType = settingType;
        const selectedEvent = settingEventOptions.find(e => e.key === settingEventKey);
        return {
          ...n,
          data: {
            ...n.data,
            type: selectedType,
            label: `${n.data.name || n.data.label}：${selectedEvent?.name || ''}`,
          }
        };
      }
      return n;
    }));
    setSettingModalOpen(false);
    setSettingNodeId(null);
    setSettingType('');
    setSettingEventOptions([]);
    setSettingEventKey('');
  };
  const handleSettingModalCancel = () => {
    setSettingModalOpen(false);
    setSettingNodeId(null);
    setSettingType('');
    setSettingEventOptions([]);
    setSettingEventKey('');
  };

  return (
    <div style={{ display: 'flex', height: '100vh', background: '#f4f6fa' }}>
      {/* 左侧 */}
      <aside style={{
        width: 260, background: '#fff', display: 'flex', flexDirection: 'column',
        justifyContent: 'flex-start', borderRight: '1.5px solid #e0e7ff', boxShadow: '2px 0 8px 0 #e0e7ff33'
      }}>
        <div style={{ padding: 24, overflowY: 'auto', flex: 1 }}>
          <h3 style={{ margin: '0 0 12px 0' }}>组件(function)</h3>
          <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
            {functionList.map(item => {
              const style = stylesMap[item.type] || {};
              return (
                <li
                  key={item.key}
                  style={{
                    marginBottom: 6,
                    background: style.backgroundColor || '#f5f5f5',
                    color: style.color || '#333',
                    border: style.borderColor ? `1.5px solid ${style.borderColor}` : '1.5px solid #eee',
                    borderRadius: 6,
                    padding: '4px 8px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 6,
                    fontSize: 13,
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    minHeight: 28,
                    maxWidth: 210,
                    cursor: 'grab',
                  }}
                  draggable
                  onDragStart={(e) => onDragStart(e, item)}
                >
                  <span className="material-icons" style={{ fontSize: 18, color: style.color || '#333', flex: 'none' }}>{style.icon || 'help'}</span>
                  <span style={{ fontSize: 13, fontWeight: 500, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>{item.name}</span>
                </li>
              );
            })}
          </ul>
          {/* 保存/导入按钮区 */}
          <div style={{ marginTop: 24, display: 'flex', flexDirection: 'column', gap: 12 }}>
            <button
              style={{ height: 40, borderRadius: 8, background: '#6366f1', color: '#fff', border: 'none' }}
              onClick={() => {
                // 导出json，包含节点、连线和输入参数
                const workspaceData = {
                  nodes,
                  edges,
                  // 保存当前的输入参数状态
                  inputParams,
                  // 添加保存时间戳
                  savedAt: new Date().toISOString(),
                  // 添加版本信息
                  version: '1.0'
                };
                const data = JSON.stringify(workspaceData, null, 2);
                const blob = new Blob([data], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'canvas.json';
                a.click();
                URL.revokeObjectURL(url);
              }}
            >保存工作区</button>
            <button
              style={{ height: 40, borderRadius: 8, background: '#60a5fa', color: '#fff', border: 'none' }}
              onClick={() => {
                document.getElementById('canvas-import-input').click();
              }}
            >上传自定义模板</button>
            <input
              id="canvas-import-input"
              type="file"
              accept="application/json"
              style={{ display: 'none' }}
              onChange={e => {
                const file = e.target.files[0];
                if (!file) return;
                const reader = new FileReader();
                reader.onload = evt => {
                  try {
                    const obj = JSON.parse(evt.target.result);
                    setNodes(obj.nodes || []);
                    setEdges(obj.edges || []);
                    // 如果文件包含inputParams，也一并导入
                    if (obj.inputParams) {
                      setInputParams(obj.inputParams);
                    }
                    // 显示导入成功信息
                    const importInfo = obj.version ? `版本 ${obj.version}` : '未知版本';
                    const savedTime = obj.savedAt ? new Date(obj.savedAt).toLocaleString() : '未知时间';
                    alert(`导入成功！\n${importInfo}\n保存时间：${savedTime}`);
                  } catch (err) {
                    alert('导入失败：文件格式错误');
                  }
                };
                reader.readAsText(file);
                e.target.value = '';
              }}
            />
            <button
              style={{ height: 40, borderRadius: 8, background: '#f44336', color: '#fff', border: 'none' }}
              onClick={() => {
                setNodes([]);
                setEdges([]);
              }}
            >清除工作区</button>
            <button
              style={{ height: 40, borderRadius: 8, background: '#22c55e', color: '#fff', border: 'none' }}
              onClick={() => {
                setShareFileName('');
                setShareError('');
                setShowShareModal(true);
              }}
            >分享</button>
          </div>
        </div>
      </aside>
      {/* 右侧：画布 */}
      <div
        ref={reactFlowWrapper}
        style={{ flex: 1, width: '100%', height: '100%', position: 'relative', background: '#f4f6fa' }}
        onDrop={onDrop}
        onDragOver={onDragOver}
      >
        {/* 右侧说明面板 */}
        {infoPanelOpen && (
          <div style={{
            position: 'fixed', right: 0, top: 0, width: 360, height: '100vh', background: '#fff',
            boxShadow: '-2px 0 16px #6366f122', zIndex: 9999, padding: 32, overflowY: 'auto',
            borderTopLeftRadius: 18, borderBottomLeftRadius: 18, display: 'flex', flexDirection: 'column', justifyContent: 'flex-start',
            background: 'linear-gradient(135deg, #f8fafc 60%, #e0e7ff 100%)'
          }}>
            <div style={{ fontWeight: 800, fontSize: 22, marginBottom: 18, color: '#6366f1', letterSpacing: 1 }}>节点说明</div>
            <div style={{ marginBottom: 18, color: '#333', fontSize: 16 }}>
              <b>名称：</b>{infoPanelData.nodeName}
            </div>
            <div style={{ marginBottom: 18, color: '#555', fontSize: 15 }}>
              <b>描述：</b>{infoPanelData.description}
            </div>
            {infoPanelData.input && (
              <div style={{ marginBottom: 18 }}>
                <b style={{ fontSize: 15 }}>输入参数：</b>
                <form>
                  <ul style={{ paddingLeft: 0, marginTop: 12 }}>
                    {Array.isArray(infoPanelData.input)
                      ? infoPanelData.input.map((item, idx) => (
                          <li key={idx} style={{ listStyle: 'none', marginBottom: 12 }}>
                            {Object.entries(item).map(([k, v]) => (
                              <div key={k} style={{ marginBottom: 10, display: 'flex', alignItems: 'center' }}>
                                <label style={{ fontWeight: 500, minWidth: 70, color: '#6366f1', fontSize: 15 }}>{k}：</label>
                                <input
                                  style={{ width: 200, marginLeft: 8, padding: '6px 10px', borderRadius: 6, border: '1.5px solid #c7d2fe', fontSize: 15, background: '#fff' }}
                                  placeholder={v}
                                  value={inputParams[k] || ''}
                                  onChange={e => setInputParams(params => ({ ...params, [k]: e.target.value }))}
                                />
                              </div>
                            ))}
                          </li>
                        ))
                      : Object.entries(infoPanelData.input).map(([k, v]) => (
                          <li key={k} style={{ listStyle: 'none', marginBottom: 12 }}>
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <label style={{ fontWeight: 500, minWidth: 70, color: '#6366f1', fontSize: 15 }}>{k}：</label>
                              <input
                                style={{ width: 200, marginLeft: 8, padding: '6px 10px', borderRadius: 6, border: '1.5px solid #c7d2fe', fontSize: 15, background: '#fff' }}
                                placeholder={v}
                                value={inputParams[k] || ''}
                                onChange={e => setInputParams(params => ({ ...params, [k]: e.target.value }))}
                              />
                            </div>
                          </li>
                        ))
                    }
                  </ul>
                </form>
              </div>
            )}
            <div style={{ display: 'flex', gap: 18, justifyContent: 'flex-end', marginTop: 32 }}>
              <button
                style={{ background: 'linear-gradient(90deg,#6366f1 60%,#818cf8 100%)', color: '#fff', border: 'none', borderRadius: 8, padding: '8px 28px', fontWeight: 700, fontSize: 16, boxShadow: '0 2px 8px #6366f133', cursor: 'pointer' }}
                onClick={() => {
                  setNodes(nds => nds.map(n => n.id === infoPanelData.nodeId
                    ? { ...n, data: { ...n.data, inputParams } }
                    : n
                  ));
                  setInfoPanelOpen(false);
                }}
              >确认</button>
              <button
                style={{ background: '#eee', color: '#333', border: 'none', borderRadius: 8, padding: '8px 28px', fontWeight: 700, fontSize: 16, boxShadow: '0 2px 8px #6366f111', cursor: 'pointer' }}
                onClick={() => setInfoPanelOpen(false)}
              >取消</button>
            </div>
          </div>
        )}
        {/* 银行业务决策器弹窗 */}
        {decisionHelperOpen && (
          <BankingDecisionNodeHelper
            onConfirm={handleDecisionHelperOk}
            onCancel={handleDecisionHelperCancel}
            eventOptions={decisionEventOptions}
            selectedEventKey={decisionSelectedEventKey}
            onEventChange={setDecisionSelectedEventKey}
          />
        )}
        {/* 弹窗 */}
        {modalOpen && (
          <div style={{
            position: 'fixed', left: 0, top: 0, width: '100vw', height: '100vh',
            background: 'rgba(0,0,0,0.18)', zIndex: 9999, display: 'flex', alignItems: 'center', justifyContent: 'center'
          }}>
            <div style={{ background: '#fff', borderRadius: 10, padding: 32, minWidth: 320, boxShadow: '0 4px 24px #0002' }}>
              <div style={{ fontWeight: 700, fontSize: 18, marginBottom: 16 }}>请选择事件</div>
              <select style={{ width: '100%', fontSize: 16, padding: 8, marginBottom: 24 }} value={selectedEventKey} onChange={e => setSelectedEventKey(e.target.value)}>
                {eventOptions.map(ev => (
                  <option key={ev.key} value={ev.key}>{ev.name}</option>
                ))}
              </select>
              <div style={{ display: 'flex', gap: 16, justifyContent: 'flex-end' }}>
                <button className="btn" onClick={handleModalOk}>确定</button>
                <button className="btn" style={{ background: '#eee', color: '#333' }} onClick={handleModalCancel}>取消</button>
              </div>
            </div>
          </div>
        )}
        {/* 设置弹窗 */}
        {settingModalOpen && (
          <ChildNodeSettingModal
            typeOptions={settingTypeOptions}
            type={settingType}
            onTypeChange={handleSettingTypeChange}
            eventOptions={settingEventOptions}
            eventKey={settingEventKey}
            onEventChange={setSettingEventKey}
            onConfirm={handleSettingModalOk}
            onCancel={handleSettingModalCancel}
            functionList={functionList}
          />
        )}
        {/* 分享弹窗 */}
        {showShareModal && (
          <div style={{
            position: 'fixed', left: 0, top: 0, width: '100vw', height: '100vh',
            background: 'rgba(0,0,0,0.18)', zIndex: 9999, display: 'flex', alignItems: 'center', justifyContent: 'center'
          }}>
            <div style={{ background: '#fff', borderRadius: 10, padding: 32, minWidth: 340, boxShadow: '0 4px 24px #0002' }}>
              <div style={{ fontWeight: 700, fontSize: 18, marginBottom: 16 }}>请输入模板文件名（不含扩展名）</div>
              <input
                autoFocus
                value={shareFileName}
                onChange={e => {
                  setShareFileName(e.target.value);
                  setShareError('');
                }}
                placeholder="仅限汉字、字母、数字、下划线、横线"
                style={{ width: '100%', fontSize: 16, padding: 8, borderRadius: 6, border: '1.5px solid #c7d2fe', marginBottom: 8 }}
                onKeyDown={e => { if (e.key === 'Enter') { document.getElementById('share-confirm-btn').click(); } }}
              />
              {shareError && <div style={{ color: 'red', fontSize: 14, marginBottom: 8 }}>{shareError}</div>}
              <div style={{ display: 'flex', gap: 16, justifyContent: 'flex-end', marginTop: 8 }}>
                <button
                  id="share-confirm-btn"
                  className="btn"
                  style={{ background: '#22c55e', color: '#fff', borderRadius: 6, padding: '6px 18px', fontWeight: 700, fontSize: 15 }}
                  onClick={async () => {
                    const domainMap = {
                      '银行领域': 'banking',
                      '教育领域': 'education',
                    };
                    const dir = domainMap[domainName] || 'other';
                    let userInput = shareFileName.trim();
                    if (!userInput) {
                      setShareError('文件名不能为空');
                      return;
                    }
                    // 允许汉字，只禁止特殊符号
                    if (!/^[\u4e00-\u9fa5\w\-]+$/.test(userInput)) {
                      setShareError('文件名只能包含汉字、字母、数字、下划线、横线');
                      return;
                    }
                    const fileName = `${dir}-${userInput}.json`;
                    try {
                      const resp = await uploadTemplate({
                        domain: dir,
                        fileName,
                        content: {
                          nodes,
                          edges,
                          // 同样保存输入参数到模板
                          inputParams,
                          savedAt: new Date().toISOString(),
                          version: '1.0'
                        }
                      });
                      if (resp.success) {
                        setShowShareModal(false);
                        alert(`模板已成功上传到 ${dir} 目录下，文件名：${fileName}`);
                      } else {
                        setShareError('上传失败：' + (resp.message || '未知错误'));
                      }
                    } catch (err) {
                      setShareError('上传失败：' + err.message);
                    }
                  }}
                >确定</button>
                <button
                  className="btn"
                  style={{ background: '#eee', color: '#333', borderRadius: 6, padding: '6px 18px', fontWeight: 700, fontSize: 15 }}
                  onClick={() => setShowShareModal(false)}
                >取消</button>
              </div>
            </div>
          </div>
        )}
        <ReactFlow
          nodes={nodes}
          edges={edges}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          fitView
          style={{ width: '100%', height: '100%' }}
        >
          <MiniMap />
          <Controls />
          <Background variant="dots" gap={16} size={1} />
        </ReactFlow>
      </div>
    </div>
  );
}

CustomNode.defaultProps = {
  onPlay: undefined,
  playingNodeId: undefined,
};

export default function NewBlankCanvasPage() {
  const [functionList, setFunctionList] = useState([]);
  const [stylesMap, setStylesMap] = useState({});
  const [eventFileName, setEventFileName] = useState('');
  const [domainName, setDomainName] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchText('/configs/domain-switch.yml')
      .then(text => {
        const doc = yaml.load(text);
        // 直接读取第一个apply-domain的name字段
        let domain = null;
        if (Array.isArray(doc?.['apply-domain'])) {
          domain = doc['apply-domain'][0];
        } else if (doc?.['apply-domain']) {
          domain = doc['apply-domain'];
        }
        if (!domain) return;
        setEventFileName(domain.event);
        const functionPath = getEnv('VITE_functions_path') + domain.function;
        const stylesPath = getEnv('VITE_styles_path') + domain.styles;
        fetchText(functionPath)
          .then(text => {
            const doc = yaml.load(text);
            setFunctionList(doc?.componentList || []);
          });
        fetchText(stylesPath)
          .then(text => {
            const doc = yaml.load(text);
            setStylesMap(doc?.componentStyles || {});
          });
        setDomainName(domain.name); // 直接设为name
      });
  }, []);

  useEffect(() => {
    if (domainName) setLoading(false);
  }, [domainName]);

  if (!domainName) {
    return <div style={{padding: 40, fontSize: 18, color: '#888'}}>领域信息加载中...</div>;
  }

  return (
    <ReactFlowProvider>
      <CanvasContent functionList={functionList} stylesMap={stylesMap} eventFileName={eventFileName} domainName={domainName} />
    </ReactFlowProvider>
  );
}