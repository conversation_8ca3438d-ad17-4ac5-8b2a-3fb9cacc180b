import React, { useEffect, useState, useMemo } from 'react';
import yaml from 'js-yaml';
import ReactFlow, { Background, Controls, MiniMap, addEdge, useNodesState, useEdgesState } from 'reactflow';
import { useNavigate } from 'react-router-dom';
import 'reactflow/dist/base.css';
import CustomNode from '../components/common/CustomNode';
import { nodeTypes as baseNodeTypes, edgeTypes } from '../components/common/reactflowTypes';

export default function TemplateLibraryPage() {
  const [domain, setDomain] = useState('');
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedTpl, setSelectedTpl] = useState(null);
  const [canvasData, setCanvasData] = useState({ nodes: [], edges: [], inputParams: {} });
  const [canvasLoading, setCanvasLoading] = useState(false);

  // 画布节点和连线状态（支持编辑）
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  const navigate = useNavigate();

  useEffect(() => {
    async function fetchDomainAndTemplates() {
      setLoading(true);
      setError('');
      try {
        const ymlRes = await fetch('/configs/domain-switch.yml');
        const ymlText = await ymlRes.text();
        const doc = yaml.load(ymlText);
        const domainObj = Array.isArray(doc?.['apply-domain']) ? doc['apply-domain'][0] : doc['apply-domain'];
        const domainName = domainObj?.name || '';
        setDomain(domainName);
        let dir = '';
        if (domainName === '银行领域') dir = 'banking';
        else if (domainName === '教育领域') dir = 'education';
        else dir = '';
        if (dir) {
          const listRes = await fetch(`/layouts/${dir}/template-list.json`);
          const list = await listRes.json();
          setTemplates(list);
        } else {
          setTemplates([]);
        }
      } catch (e) {
        setError('模板加载失败: ' + e.message);
        setTemplates([]);
      } finally {
        setLoading(false);
      }
    }
    fetchDomainAndTemplates();
  }, []);

  // 选中模板后加载画布数据
  const handleTplClick = async (tpl) => {
    setSelectedTpl(tpl.file);
    setCanvasLoading(true);
    setCanvasData({ nodes: [], edges: [] });
    let dir = domain === '银行领域' ? 'banking' : 'education';
    try {
      const res = await fetch(`/layouts/${dir}/${tpl.file}`);
      const data = await res.json();
      setCanvasData({
        nodes: data.nodes || [],
        edges: data.edges || [],
        inputParams: data.inputParams || {},
      });
      setNodes(data.nodes || []);
      setEdges(data.edges || []);
    } catch (e) {
      setCanvasData({ nodes: [], edges: [], inputParams: {} });
      setNodes([]);
      setEdges([]);
    } finally {
      setCanvasLoading(false);
    }
  };

  // 支持节点label编辑
  const handleLabelEdit = (id, newLabel) => {
    setNodes(nds => nds.map(n => n.id === id ? { ...n, data: { ...n.data, label: newLabel } } : n));
  };

  // 支持连线
  const onConnect = (params) => setEdges(eds => addEdge({ ...params, type: 'dashed' }, eds));

  // 编辑/执行按钮点击
  const handleEdit = () => {
    if (!selectedTpl) return;
    navigate('/new/blank', {
      state: {
        template: {
          nodes,
          edges,
          inputParams: canvasData.inputParams || {}
        }
      }
    });
  };

  // 删除节点
  const handleDeleteNode = (id) => {
    setNodes(nds => nds.filter(n => n.id !== id));
    setEdges(eds => eds.filter(e => e.source !== id && e.target !== id));
  };

  // nodeTypes/edgeTypes共通，传递onDelete
  const nodeTypes = useMemo(() => ({
    custom: (props) => <CustomNode {...props} onLabelEdit={handleLabelEdit} />
  }), []);

  return (
    <div style={{ display: 'flex', height: '100vh', background: '#f4f6fa' }}>
      {/* 左侧模板列表 */}
      <aside style={{
        width: 260, background: '#fff', borderRight: '1.5px solid #e0e7ff', boxShadow: '2px 0 8px 0 #e0e7ff33',
        display: 'flex', flexDirection: 'column', padding: 0
      }}>
        <div style={{ padding: 24, flex: 1, overflowY: 'auto' }}>
          <h3 style={{ margin: '0 0 12px 0' }}>模板库</h3>
          <div style={{ marginBottom: 12, color: '#666' }}>当前领域：{domain || '未识别'}</div>
          {loading && <div>加载中...</div>}
          {error && <div style={{ color: 'red' }}>{error}</div>}
          <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
            {templates.map(tpl => (
              <li
                key={tpl.file}
                style={{
                  background: selectedTpl === tpl.file ? '#ede9fe' : '#f4f6fa',
                  color: selectedTpl === tpl.file ? '#6366f1' : '#333',
                  borderRadius: 6,
                  padding: '8px 12px',
                  marginBottom: 8,
                  cursor: 'pointer',
                  fontWeight: selectedTpl === tpl.file ? 700 : 400,
                  border: selectedTpl === tpl.file ? '1.5px solid #6366f1' : '1.5px solid #eee',
                  transition: 'all 0.2s'
                }}
                onClick={() => handleTplClick(tpl)}
              >
                {tpl.name}
              </li>
            ))}
          </ul>
          {(!loading && templates.length === 0 && !error) && <div>暂无模板</div>}
        </div>
        {/* 编辑/执行按钮 */}
        <div style={{ padding: 24, borderTop: '1.5px solid #e0e7ff' }}>
          <button
            style={{
              width: '100%',
              height: 40,
              borderRadius: 8,
              background: selectedTpl ? '#6366f1' : '#ccc',
              color: '#fff',
              border: 'none',
              fontWeight: 700,
              fontSize: 16,
              cursor: selectedTpl ? 'pointer' : 'not-allowed'
            }}
            disabled={!selectedTpl}
            onClick={handleEdit}
          >
            编辑/执行
          </button>
        </div>
      </aside>
      {/* 右侧画布 */}
      <div style={{ flex: 1, width: '100%', height: '100%', position: 'relative', background: '#f4f6fa', display: 'flex', flexDirection: 'column' }}>
        <div style={{ flex: 1, minHeight: 0, position: 'relative' }}>
          {canvasLoading && <div style={{ position: 'absolute', left: 0, top: 0, right: 0, bottom: 0, zIndex: 10, background: 'rgba(255,255,255,0.7)', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>画布加载中...</div>}
          <ReactFlow
            nodes={nodes}
            edges={edges}
            nodeTypes={nodeTypes}
            edgeTypes={edgeTypes}
            fitView
            nodesDraggable={true}
            nodesConnectable={true}
            elementsSelectable={true}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            style={{ width: '100%', height: '100%' }}
          >
            <MiniMap />
            <Controls />
            <Background variant="dots" gap={16} size={1} />
          </ReactFlow>
        </div>
      </div>
    </div>
  );
} 